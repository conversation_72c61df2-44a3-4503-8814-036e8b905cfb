namespace Liam.TcpClient.Exceptions;

/// <summary>
/// TCP客户端基础异常
/// </summary>
public class TcpClientException : Exception
{
    /// <summary>
    /// 初始化TCP客户端异常
    /// </summary>
    public TcpClientException() : base()
    {
    }

    /// <summary>
    /// 初始化TCP客户端异常
    /// </summary>
    /// <param name="message">异常消息</param>
    public TcpClientException(string message) : base(message)
    {
    }

    /// <summary>
    /// 初始化TCP客户端异常
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="innerException">内部异常</param>
    public TcpClientException(string message, Exception innerException) : base(message, innerException)
    {
    }
}

/// <summary>
/// 连接异常
/// </summary>
public class ConnectionException : TcpClientException
{
    /// <summary>
    /// 初始化连接异常
    /// </summary>
    public ConnectionException() : base()
    {
    }

    /// <summary>
    /// 初始化连接异常
    /// </summary>
    /// <param name="message">异常消息</param>
    public ConnectionException(string message) : base(message)
    {
    }

    /// <summary>
    /// 初始化连接异常
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="innerException">内部异常</param>
    public ConnectionException(string message, Exception innerException) : base(message, innerException)
    {
    }
}

/// <summary>
/// 连接超时异常
/// </summary>
public class ConnectionTimeoutException : ConnectionException
{
    /// <summary>
    /// 超时时间
    /// </summary>
    public TimeSpan Timeout { get; }

    /// <summary>
    /// 初始化连接超时异常
    /// </summary>
    /// <param name="timeout">超时时间</param>
    public ConnectionTimeoutException(TimeSpan timeout) 
        : base($"连接超时，超时时间：{timeout.TotalSeconds}秒")
    {
        Timeout = timeout;
    }

    /// <summary>
    /// 初始化连接超时异常
    /// </summary>
    /// <param name="timeout">超时时间</param>
    /// <param name="message">异常消息</param>
    public ConnectionTimeoutException(TimeSpan timeout, string message) : base(message)
    {
        Timeout = timeout;
    }

    /// <summary>
    /// 初始化连接超时异常
    /// </summary>
    /// <param name="timeout">超时时间</param>
    /// <param name="message">异常消息</param>
    /// <param name="innerException">内部异常</param>
    public ConnectionTimeoutException(TimeSpan timeout, string message, Exception innerException) 
        : base(message, innerException)
    {
        Timeout = timeout;
    }
}

/// <summary>
/// 认证异常
/// </summary>
public class AuthenticationException : TcpClientException
{
    /// <summary>
    /// 初始化认证异常
    /// </summary>
    public AuthenticationException() : base()
    {
    }

    /// <summary>
    /// 初始化认证异常
    /// </summary>
    /// <param name="message">异常消息</param>
    public AuthenticationException(string message) : base(message)
    {
    }

    /// <summary>
    /// 初始化认证异常
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="innerException">内部异常</param>
    public AuthenticationException(string message, Exception innerException) : base(message, innerException)
    {
    }
}

/// <summary>
/// 心跳超时异常
/// </summary>
public class HeartbeatTimeoutException : TcpClientException
{
    /// <summary>
    /// 超时时间
    /// </summary>
    public TimeSpan Timeout { get; }

    /// <summary>
    /// 初始化心跳超时异常
    /// </summary>
    /// <param name="timeout">超时时间</param>
    public HeartbeatTimeoutException(TimeSpan timeout) 
        : base($"心跳超时，超时时间：{timeout.TotalSeconds}秒")
    {
        Timeout = timeout;
    }

    /// <summary>
    /// 初始化心跳超时异常
    /// </summary>
    /// <param name="timeout">超时时间</param>
    /// <param name="message">异常消息</param>
    public HeartbeatTimeoutException(TimeSpan timeout, string message) : base(message)
    {
        Timeout = timeout;
    }

    /// <summary>
    /// 初始化心跳超时异常
    /// </summary>
    /// <param name="timeout">超时时间</param>
    /// <param name="message">异常消息</param>
    /// <param name="innerException">内部异常</param>
    public HeartbeatTimeoutException(TimeSpan timeout, string message, Exception innerException) 
        : base(message, innerException)
    {
        Timeout = timeout;
    }
}

/// <summary>
/// 消息异常
/// </summary>
public class MessageException : TcpClientException
{
    /// <summary>
    /// 初始化消息异常
    /// </summary>
    public MessageException() : base()
    {
    }

    /// <summary>
    /// 初始化消息异常
    /// </summary>
    /// <param name="message">异常消息</param>
    public MessageException(string message) : base(message)
    {
    }

    /// <summary>
    /// 初始化消息异常
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="innerException">内部异常</param>
    public MessageException(string message, Exception innerException) : base(message, innerException)
    {
    }
}

/// <summary>
/// SSL异常
/// </summary>
public class SslException : TcpClientException
{
    /// <summary>
    /// 初始化SSL异常
    /// </summary>
    public SslException() : base()
    {
    }

    /// <summary>
    /// 初始化SSL异常
    /// </summary>
    /// <param name="message">异常消息</param>
    public SslException(string message) : base(message)
    {
    }

    /// <summary>
    /// 初始化SSL异常
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="innerException">内部异常</param>
    public SslException(string message, Exception innerException) : base(message, innerException)
    {
    }
}
