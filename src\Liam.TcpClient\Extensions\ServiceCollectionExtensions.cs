using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Logging;
using Liam.TcpClient.Interfaces;
using Liam.TcpClient.Models;
using Liam.TcpClient.Services;

namespace Liam.TcpClient.Extensions;

/// <summary>
/// 服务集合扩展方法
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加TCP客户端服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">客户端配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddTcpClient(this IServiceCollection services, TcpClientConfig configuration)
    {
        ArgumentNullException.ThrowIfNull(services);
        ArgumentNullException.ThrowIfNull(configuration);

        // 验证配置
        var validation = configuration.Validate();
        if (!validation.IsValid)
        {
            throw new ArgumentException($"配置无效：{string.Join(", ", validation.Errors)}", nameof(configuration));
        }

        // 注册配置
        services.TryAddSingleton(configuration);

        // 注册核心服务
        services.TryAddTransient<IConnectionManager, ConnectionManager>();
        services.TryAddTransient<IMessageHandler, MessageHandler>();
        services.TryAddTransient<IHeartbeatManager, HeartbeatManager>();

        // 注册TCP客户端
        services.TryAddTransient<ITcpClient, Services.TcpClient>();

        return services;
    }

    /// <summary>
    /// 添加TCP客户端服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configureOptions">配置选项</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddTcpClient(this IServiceCollection services, Action<TcpClientConfig> configureOptions)
    {
        ArgumentNullException.ThrowIfNull(services);
        ArgumentNullException.ThrowIfNull(configureOptions);

        var configuration = new TcpClientConfig();
        configureOptions(configuration);

        return services.AddTcpClient(configuration);
    }

    /// <summary>
    /// 添加TCP客户端服务（使用默认配置）
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="host">服务器主机</param>
    /// <param name="port">服务器端口</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddTcpClient(this IServiceCollection services, string host, int port)
    {
        ArgumentNullException.ThrowIfNull(services);
        ArgumentNullException.ThrowIfNull(host);

        var configuration = TcpClientConfig.CreateDefault(host, port);
        return services.AddTcpClient(configuration);
    }

    /// <summary>
    /// 添加SSL TCP客户端服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="host">服务器主机</param>
    /// <param name="port">服务器端口</param>
    /// <param name="serverName">SSL服务器名称</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddSslTcpClient(this IServiceCollection services, string host, int port, string? serverName = null)
    {
        ArgumentNullException.ThrowIfNull(services);
        ArgumentNullException.ThrowIfNull(host);

        var configuration = TcpClientConfig.CreateSslConfig(host, port, serverName);
        return services.AddTcpClient(configuration);
    }

    /// <summary>
    /// 添加命名TCP客户端服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="name">客户端名称</param>
    /// <param name="configuration">客户端配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddNamedTcpClient(this IServiceCollection services, string name, TcpClientConfig configuration)
    {
        ArgumentNullException.ThrowIfNull(services);
        ArgumentNullException.ThrowIfNull(name);
        ArgumentNullException.ThrowIfNull(configuration);

        // 验证配置
        var validation = configuration.Validate();
        if (!validation.IsValid)
        {
            throw new ArgumentException($"配置无效：{string.Join(", ", validation.Errors)}", nameof(configuration));
        }

        // 注册命名配置
        services.Configure<TcpClientOptions>(name, options =>
        {
            options.Configuration = configuration;
        });

        // 注册命名TCP客户端工厂
        services.TryAddSingleton<ITcpClientFactory, TcpClientFactory>();

        return services;
    }

    /// <summary>
    /// 添加命名TCP客户端服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="name">客户端名称</param>
    /// <param name="configureOptions">配置选项</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddNamedTcpClient(this IServiceCollection services, string name, Action<TcpClientConfig> configureOptions)
    {
        ArgumentNullException.ThrowIfNull(services);
        ArgumentNullException.ThrowIfNull(name);
        ArgumentNullException.ThrowIfNull(configureOptions);

        var configuration = new TcpClientConfig();
        configureOptions(configuration);

        return services.AddNamedTcpClient(name, configuration);
    }

    /// <summary>
    /// 添加TCP客户端池
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">客户端配置</param>
    /// <param name="poolSize">连接池大小</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddTcpClientPool(this IServiceCollection services, TcpClientConfig configuration, int poolSize = 10)
    {
        ArgumentNullException.ThrowIfNull(services);
        ArgumentNullException.ThrowIfNull(configuration);

        if (poolSize <= 0)
        {
            throw new ArgumentException("连接池大小必须大于0", nameof(poolSize));
        }

        // 启用连接池配置
        configuration.ConnectionPoolConfig = new ConnectionPoolConfig
        {
            Enabled = true,
            PoolSize = poolSize
        };

        // 注册连接池服务
        services.TryAddSingleton<ITcpClientPool, TcpClientPool>();

        return services.AddTcpClient(configuration);
    }
}

/// <summary>
/// TCP客户端选项
/// </summary>
public class TcpClientOptions
{
    /// <summary>
    /// 客户端配置
    /// </summary>
    public TcpClientConfig? Configuration { get; set; }
}

/// <summary>
/// TCP客户端工厂接口
/// </summary>
public interface ITcpClientFactory
{
    /// <summary>
    /// 创建命名TCP客户端
    /// </summary>
    /// <param name="name">客户端名称</param>
    /// <returns>TCP客户端</returns>
    ITcpClient CreateClient(string name);

    /// <summary>
    /// 创建TCP客户端
    /// </summary>
    /// <param name="configuration">客户端配置</param>
    /// <returns>TCP客户端</returns>
    ITcpClient CreateClient(TcpClientConfig configuration);
}

/// <summary>
/// TCP客户端工厂实现
/// </summary>
public class TcpClientFactory : ITcpClientFactory
{
    private readonly IServiceProvider _serviceProvider;

    /// <summary>
    /// 初始化TCP客户端工厂
    /// </summary>
    /// <param name="serviceProvider">服务提供程序</param>
    public TcpClientFactory(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
    }

    /// <summary>
    /// 创建命名TCP客户端
    /// </summary>
    /// <param name="name">客户端名称</param>
    /// <returns>TCP客户端</returns>
    public ITcpClient CreateClient(string name)
    {
        ArgumentNullException.ThrowIfNull(name);

        // 获取命名配置
        var options = _serviceProvider.GetRequiredService<Microsoft.Extensions.Options.IOptionsSnapshot<TcpClientOptions>>();
        var configuration = options.Get(name).Configuration;

        if (configuration == null)
        {
            throw new InvalidOperationException($"未找到名为 '{name}' 的TCP客户端配置");
        }

        return CreateClient(configuration);
    }

    /// <summary>
    /// 创建TCP客户端
    /// </summary>
    /// <param name="configuration">客户端配置</param>
    /// <returns>TCP客户端</returns>
    public ITcpClient CreateClient(TcpClientConfig configuration)
    {
        ArgumentNullException.ThrowIfNull(configuration);

        var logger = _serviceProvider.GetRequiredService<ILogger<Services.TcpClient>>();
        var connectionManagerLogger = _serviceProvider.GetRequiredService<ILogger<ConnectionManager>>();
        var messageHandlerLogger = _serviceProvider.GetRequiredService<ILogger<MessageHandler>>();
        var heartbeatManagerLogger = _serviceProvider.GetRequiredService<ILogger<HeartbeatManager>>();

        var connectionManager = new ConnectionManager(connectionManagerLogger);
        var messageHandler = new MessageHandler(messageHandlerLogger);
        var heartbeatManager = new HeartbeatManager(heartbeatManagerLogger, messageHandler);

        return new Services.TcpClient(configuration, logger, connectionManager, messageHandler, heartbeatManager);
    }
}

/// <summary>
/// TCP客户端池接口
/// </summary>
public interface ITcpClientPool : IDisposable, IAsyncDisposable
{
    /// <summary>
    /// 获取客户端
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>TCP客户端</returns>
    Task<ITcpClient> GetClientAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 归还客户端
    /// </summary>
    /// <param name="client">TCP客户端</param>
    /// <returns>归还任务</returns>
    Task ReturnClientAsync(ITcpClient client);

    /// <summary>
    /// 获取池统计信息
    /// </summary>
    /// <returns>池统计信息</returns>
    PoolStatistics GetStatistics();
}

/// <summary>
/// TCP客户端池实现
/// </summary>
public class TcpClientPool : ITcpClientPool
{
    private readonly ITcpClientFactory _clientFactory;
    private readonly TcpClientConfig _configuration;
    private readonly ILogger<TcpClientPool> _logger;
    private readonly Queue<ITcpClient> _availableClients = new();
    private readonly HashSet<ITcpClient> _allClients = new();
    private readonly SemaphoreSlim _semaphore;
    private readonly object _lockObject = new();
    private bool _disposed;

    /// <summary>
    /// 初始化TCP客户端池
    /// </summary>
    /// <param name="clientFactory">客户端工厂</param>
    /// <param name="configuration">客户端配置</param>
    /// <param name="logger">日志记录器</param>
    public TcpClientPool(ITcpClientFactory clientFactory, TcpClientConfig configuration, ILogger<TcpClientPool> logger)
    {
        _clientFactory = clientFactory ?? throw new ArgumentNullException(nameof(clientFactory));
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        var poolSize = configuration.ConnectionPoolConfig?.PoolSize ?? 10;
        _semaphore = new SemaphoreSlim(poolSize, poolSize);

        _logger.LogInformation("TCP客户端池已初始化，池大小：{PoolSize}", poolSize);
    }

    /// <summary>
    /// 获取客户端
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>TCP客户端</returns>
    public async Task<ITcpClient> GetClientAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
        {
            throw new ObjectDisposedException(nameof(TcpClientPool));
        }

        await _semaphore.WaitAsync(cancellationToken).ConfigureAwait(false);

        lock (_lockObject)
        {
            // 尝试从池中获取可用客户端
            if (_availableClients.Count > 0)
            {
                var client = _availableClients.Dequeue();
                if (client.IsConnected)
                {
                    return client;
                }
                else
                {
                    // 客户端已断开，移除并创建新的
                    _allClients.Remove(client);
                    client.Dispose();
                }
            }

            // 创建新客户端
            var newClient = _clientFactory.CreateClient(_configuration);
            _allClients.Add(newClient);
            return newClient;
        }
    }

    /// <summary>
    /// 归还客户端
    /// </summary>
    /// <param name="client">TCP客户端</param>
    /// <returns>归还任务</returns>
    public async Task ReturnClientAsync(ITcpClient client)
    {
        ArgumentNullException.ThrowIfNull(client);

        if (_disposed)
        {
            client.Dispose();
            return;
        }

        lock (_lockObject)
        {
            if (_allClients.Contains(client) && client.IsConnected)
            {
                _availableClients.Enqueue(client);
            }
            else
            {
                _allClients.Remove(client);
                client.Dispose();
            }
        }

        _semaphore.Release();
        await Task.CompletedTask;
    }

    /// <summary>
    /// 获取池统计信息
    /// </summary>
    /// <returns>池统计信息</returns>
    public PoolStatistics GetStatistics()
    {
        lock (_lockObject)
        {
            return new PoolStatistics
            {
                TotalClients = _allClients.Count,
                AvailableClients = _availableClients.Count,
                BusyClients = _allClients.Count - _availableClients.Count,
                MaxPoolSize = _configuration.ConnectionPoolConfig?.PoolSize ?? 10
            };
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
        {
            return;
        }

        _disposed = true;

        lock (_lockObject)
        {
            foreach (var client in _allClients)
            {
                client.Dispose();
            }
            _allClients.Clear();
            _availableClients.Clear();
        }

        _semaphore.Dispose();
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 异步释放资源
    /// </summary>
    public async ValueTask DisposeAsync()
    {
        if (_disposed)
        {
            return;
        }

        _disposed = true;

        List<ITcpClient> clients;
        lock (_lockObject)
        {
            clients = new List<ITcpClient>(_allClients);
            _allClients.Clear();
            _availableClients.Clear();
        }

        foreach (var client in clients)
        {
            await client.DisposeAsync().ConfigureAwait(false);
        }

        _semaphore.Dispose();
        GC.SuppressFinalize(this);
    }
}

/// <summary>
/// 池统计信息
/// </summary>
public class PoolStatistics
{
    /// <summary>
    /// 总客户端数
    /// </summary>
    public int TotalClients { get; set; }

    /// <summary>
    /// 可用客户端数
    /// </summary>
    public int AvailableClients { get; set; }

    /// <summary>
    /// 忙碌客户端数
    /// </summary>
    public int BusyClients { get; set; }

    /// <summary>
    /// 最大池大小
    /// </summary>
    public int MaxPoolSize { get; set; }

    /// <summary>
    /// 池使用率
    /// </summary>
    public double UtilizationRate => MaxPoolSize > 0 ? (double)BusyClients / MaxPoolSize * 100 : 0;
}
