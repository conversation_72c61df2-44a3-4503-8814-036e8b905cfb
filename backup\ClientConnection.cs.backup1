using System.Net;
using System.Net.Sockets;
using System.Net.Security;
using Liam.TcpServer.Constants;

namespace Liam.TcpServer.Models;

/// <summary>
/// 客户端连接信息
/// </summary>
public class ClientConnection : IDisposable
{
    private bool _disposed = false;

    /// <summary>
    /// 连接唯一标识符
    /// </summary>
    public string Id { get; }

    /// <summary>
    /// TCP客户端
    /// </summary>
    public TcpClient TcpClient { get; }

    /// <summary>
    /// 网络流
    /// </summary>
    public Stream NetworkStream { get; private set; }

    /// <summary>
    /// 客户端IP地址
    /// </summary>
    public IPAddress ClientIpAddress { get; }

    /// <summary>
    /// 客户端端口
    /// </summary>
    public int ClientPort { get; }

    /// <summary>
    /// 连接建立时间
    /// </summary>
    public DateTime ConnectedAt { get; }

    /// <summary>
    /// 最后活动时间
    /// </summary>
    public DateTime LastActivityAt { get; set; }

    /// <summary>
    /// 最后心跳时间
    /// </summary>
    public DateTime LastHeartbeatAt { get; set; }

    /// <summary>
    /// 连接状态
    /// </summary>
    public string Status { get; set; }

    /// <summary>
    /// 是否已认证
    /// </summary>
    public bool IsAuthenticated { get; set; }

    /// <summary>
    /// 认证用户信息
    /// </summary>
    public string? AuthenticatedUser { get; set; }

    /// <summary>
    /// 是否启用SSL
    /// </summary>
    public bool IsSslEnabled { get; private set; }

    /// <summary>
    /// SSL流（如果启用SSL）
    /// </summary>
    public SslStream? SslStream { get; private set; }

    /// <summary>
    /// 连接统计信息
    /// </summary>
    public ConnectionStatistics Statistics { get; }

    /// <summary>
    /// 连接属性（用于存储自定义数据）
    /// </summary>
    public Dictionary<string, object> Properties { get; }

    /// <summary>
    /// 取消令牌源
    /// </summary>
    public CancellationTokenSource CancellationTokenSource { get; }

    /// <summary>
    /// 取消令牌
    /// </summary>
    public CancellationToken CancellationToken => CancellationTokenSource.Token;

    /// <summary>
    /// 是否已连接
    /// </summary>
    public bool IsConnected => TcpClient.Connected && !CancellationToken.IsCancellationRequested;

    /// <summary>
    /// 连接持续时间
    /// </summary>
    public TimeSpan ConnectionDuration => DateTime.UtcNow - ConnectedAt;

    /// <summary>
    /// 空闲时间
    /// </summary>
    public TimeSpan IdleTime => DateTime.UtcNow - LastActivityAt;

    /// <summary>
    /// 初始化客户端连接
    /// </summary>
    /// <param name="tcpClient">TCP客户端</param>
    public ClientConnection(TcpClient tcpClient)
    {
        ArgumentNullException.ThrowIfNull(tcpClient);

        Id = Guid.NewGuid().ToString("N");
        TcpClient = tcpClient;
        NetworkStream = tcpClient.GetStream();

        var remoteEndPoint = (IPEndPoint)tcpClient.Client.RemoteEndPoint!;
        ClientIpAddress = remoteEndPoint.Address;
        ClientPort = remoteEndPoint.Port;

        var now = DateTime.UtcNow;
        ConnectedAt = now;
        LastActivityAt = now;
        LastHeartbeatAt = now;

        Status = TcpServerConstants.ConnectionStates.Connected;
        IsAuthenticated = false;
        IsSslEnabled = false;

        Statistics = new ConnectionStatistics();
        Properties = new Dictionary<string, object>();
        CancellationTokenSource = new CancellationTokenSource();
    }

    /// <summary>
    /// 启用SSL
    /// </summary>
    /// <param name="sslStream">SSL流</param>
    public void EnableSsl(SslStream sslStream)
    {
        ArgumentNullException.ThrowIfNull(sslStream);
        
        SslStream = sslStream;
        NetworkStream = sslStream;
        IsSslEnabled = true;
    }

    /// <summary>
    /// 更新最后活动时间
    /// </summary>
    public void UpdateLastActivity()
    {
        LastActivityAt = DateTime.UtcNow;
    }

    /// <summary>
    /// 更新最后心跳时间
    /// </summary>
    public void UpdateLastHeartbeat()
    {
        LastHeartbeatAt = DateTime.UtcNow;
        UpdateLastActivity();
    }

    /// <summary>
    /// 设置认证状态
    /// </summary>
    /// <param name="isAuthenticated">是否已认证</param>
    /// <param name="user">认证用户</param>
    public void SetAuthentication(bool isAuthenticated, string? user = null)
    {
        IsAuthenticated = isAuthenticated;
        AuthenticatedUser = user;
    }

    /// <summary>
    /// 获取连接属性
    /// </summary>
    /// <typeparam name="T">属性类型</typeparam>
    /// <param name="key">属性键</param>
    /// <returns>属性值</returns>
    public T? GetProperty<T>(string key)
    {
        ArgumentNullException.ThrowIfNull(key);
        
        if (Properties.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        
        return default;
    }

    /// <summary>
    /// 设置连接属性
    /// </summary>
    /// <param name="key">属性键</param>
    /// <param name="value">属性值</param>
    public void SetProperty(string key, object value)
    {
        ArgumentNullException.ThrowIfNull(key);
        ArgumentNullException.ThrowIfNull(value);
        
        Properties[key] = value;
    }

    /// <summary>
    /// 移除连接属性
    /// </summary>
    /// <param name="key">属性键</param>
    /// <returns>是否成功移除</returns>
    public bool RemoveProperty(string key)
    {
        ArgumentNullException.ThrowIfNull(key);
        return Properties.Remove(key);
    }

    /// <summary>
    /// 断开连接
    /// </summary>
    public void Disconnect()
    {
        if (!CancellationTokenSource.IsCancellationRequested)
        {
            Status = TcpServerConstants.ConnectionStates.Disconnecting;
            CancellationTokenSource.Cancel();
        }
    }

    /// <summary>
    /// 获取连接信息摘要
    /// </summary>
    /// <returns>连接信息摘要</returns>
    public string GetConnectionSummary()
    {
        return $"Connection[{Id[..8]}] {ClientIpAddress}:{ClientPort} " +
               $"Status:{Status} Duration:{ConnectionDuration:hh\\:mm\\:ss} " +
               $"Idle:{IdleTime:hh\\:mm\\:ss} SSL:{IsSslEnabled} Auth:{IsAuthenticated}";
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    /// <param name="disposing">是否正在释放</param>
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            Status = TcpServerConstants.ConnectionStates.Disconnected;
            
            try
            {
                CancellationTokenSource.Cancel();
            }
            catch
            {
                // 忽略取消异常
            }

            try
            {
                SslStream?.Dispose();
            }
            catch
            {
                // 忽略SSL流释放异常
            }

            try
            {
                NetworkStream?.Dispose();
            }
            catch
            {
                // 忽略网络流释放异常
            }

            try
            {
                TcpClient?.Close();
            }
            catch
            {
                // 忽略TCP客户端关闭异常
            }

            try
            {
                CancellationTokenSource.Dispose();
            }
            catch
            {
                // 忽略取消令牌源释放异常
            }

            _disposed = true;
        }
    }

    /// <summary>
    /// 析构函数
    /// </summary>
    ~ClientConnection()
    {
        Dispose(false);
    }

    /// <summary>
    /// 重写ToString方法
    /// </summary>
    /// <returns>字符串表示</returns>
    public override string ToString()
    {
        return GetConnectionSummary();
    }
}
