using System.Net;
using System.Net.Sockets;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Liam.TcpServer.Constants;
using Liam.TcpServer.Events;
using Liam.TcpServer.Exceptions;
using Liam.TcpServer.Interfaces;
using Liam.TcpServer.Models;
using Liam.TcpServer.Handlers;

namespace Liam.TcpServer.Services;

/// <summary>
/// TCP服务器实现
/// </summary>
public class TcpServer : ITcpServer
{
    private readonly TcpServerConfig _configuration;
    private readonly IConnectionManager _connectionManager;
    private readonly ISecurityManager _securityManager;
    private readonly IMessageHandler _messageHandler;
    private readonly ILogger<TcpServer>? _logger;
    private readonly HeartbeatHandler? _heartbeatHandler;

    private TcpListener? _tcpListener;
    private CancellationTokenSource? _cancellationTokenSource;
    private Task? _acceptTask;
    private string _status = "Stopped";
    private DateTime? _startedAt;
    private bool _disposed = false;

    // 统计信息
    private long _totalConnections = 0;
    private int _maxConcurrentConnections = 0;
    private long _totalBytesSent = 0;
    private long _totalBytesReceived = 0;
    private long _totalMessagesSent = 0;
    private long _totalMessagesReceived = 0;
    private long _totalErrors = 0;

    /// <summary>
    /// 服务器配置
    /// </summary>
    public TcpServerConfig Configuration => _configuration;

    /// <summary>
    /// 服务器是否正在运行
    /// </summary>
    public bool IsRunning => _status == "Running";

    /// <summary>
    /// 当前连接数
    /// </summary>
    public int ConnectionCount => _connectionManager.ConnectionCount;

    /// <summary>
    /// 服务器状态
    /// </summary>
    public string Status => _status;

    /// <summary>
    /// 服务器启动时间
    /// </summary>
    public DateTime? StartedAt => _startedAt;

    /// <summary>
    /// 服务器运行时间
    /// </summary>
    public TimeSpan? Uptime => _startedAt.HasValue ? DateTime.UtcNow - _startedAt.Value : null;

    /// <summary>
    /// 客户端连接事件
    /// </summary>
    public event EventHandler<ClientConnectedEventArgs>? ClientConnected;

    /// <summary>
    /// 客户端断开连接事件
    /// </summary>
    public event EventHandler<ClientDisconnectedEventArgs>? ClientDisconnected;

    /// <summary>
    /// 数据接收事件
    /// </summary>
    public event EventHandler<DataReceivedEventArgs>? DataReceived;

    /// <summary>
    /// 数据发送事件
    /// </summary>
    public event EventHandler<DataSentEventArgs>? DataSent;

    /// <summary>
    /// 错误事件
    /// </summary>
    public event EventHandler<TcpServerErrorEventArgs>? Error;

    /// <summary>
    /// 服务器状态变更事件
    /// </summary>
    public event EventHandler<ServerStatusChangedEventArgs>? StatusChanged;

    /// <summary>
    /// 心跳事件
    /// </summary>
    public event EventHandler<HeartbeatEventArgs>? Heartbeat;

    /// <summary>
    /// 初始化TCP服务器
    /// </summary>
    /// <param name="configuration">服务器配置</param>
    /// <param name="connectionManager">连接管理器</param>
    /// <param name="securityManager">安全管理器</param>
    /// <param name="messageHandler">消息处理器</param>
    /// <param name="logger">日志记录器</param>
    /// <param name="loggerFactory">日志记录器工厂</param>
    public TcpServer(
        IOptions<TcpServerConfig> configuration,
        IConnectionManager connectionManager,
        ISecurityManager securityManager,
        IMessageHandler messageHandler,
        ILogger<TcpServer>? logger = null,
        ILoggerFactory? loggerFactory = null)
    {
        _configuration = configuration?.Value ?? throw new ArgumentNullException(nameof(configuration));
        _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));
        _securityManager = securityManager ?? throw new ArgumentNullException(nameof(securityManager));
        _messageHandler = messageHandler ?? throw new ArgumentNullException(nameof(messageHandler));
        _logger = logger;

        // 验证配置
        var validationResult = _configuration.Validate();
        if (!validationResult.IsValid)
        {
            throw new ConfigurationValidationException($"配置验证失败: {string.Join(", ", validationResult.Errors)}");
        }

        // 创建心跳处理器
        if (_configuration.EnableHeartbeat)
        {
            var heartbeatLogger = loggerFactory?.CreateLogger<HeartbeatHandler>();
            _heartbeatHandler = new HeartbeatHandler(_connectionManager, _configuration, heartbeatLogger);
            _heartbeatHandler.HeartbeatSent += OnHeartbeatSent;
            _heartbeatHandler.HeartbeatTimeout += OnHeartbeatTimeout;
        }

        _logger?.LogInformation("TCP服务器已初始化，端口: {Port}", _configuration.Port);
    }

    /// <summary>
    /// 启动服务器
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>启动任务</returns>
    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        if (IsRunning)
        {
            throw new ServerStartupException("服务器已在运行中");
        }

        try
        {
            ChangeStatus("Starting");

            _cancellationTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
            
            // 创建TCP监听器
            _tcpListener = new TcpListener(_configuration.ListenAddress, _configuration.Port);
            _tcpListener.Start(_configuration.ConnectionBacklog);

            _startedAt = DateTime.UtcNow;
            ChangeStatus("Running");

            // 开始接受连接
            _acceptTask = AcceptConnectionsAsync(_cancellationTokenSource.Token);

            _logger?.LogInformation("TCP服务器已启动，监听地址: {ListenAddress}:{Port}", 
                _configuration.ListenAddress, _configuration.Port);

            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            ChangeStatus("Error");
            _logger?.LogError(ex, "启动TCP服务器时发生异常");
            throw new ServerStartupException("启动服务器失败", ex);
        }
    }

    /// <summary>
    /// 停止服务器
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>停止任务</returns>
    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        if (!IsRunning)
        {
            return;
        }

        try
        {
            ChangeStatus("Stopping");

            // 取消接受新连接
            _cancellationTokenSource?.Cancel();

            // 停止TCP监听器
            _tcpListener?.Stop();

            // 等待接受任务完成
            if (_acceptTask != null)
            {
                try
                {
                    await _acceptTask.WaitAsync(TimeSpan.FromSeconds(5), cancellationToken);
                }
                catch (TimeoutException)
                {
                    _logger?.LogWarning("等待接受任务完成超时");
                }
            }

            // 断开所有客户端连接
            await _connectionManager.DisconnectAllAsync("服务器关闭");

            ChangeStatus("Stopped");
            _startedAt = null;

            _logger?.LogInformation("TCP服务器已停止");
        }
        catch (Exception ex)
        {
            ChangeStatus("Error");
            _logger?.LogError(ex, "停止TCP服务器时发生异常");
            throw;
        }
    }

    /// <summary>
    /// 重启服务器
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>重启任务</returns>
    public async Task RestartAsync(CancellationToken cancellationToken = default)
    {
        _logger?.LogInformation("重启TCP服务器");
        
        await StopAsync(cancellationToken);
        await Task.Delay(1000, cancellationToken); // 等待1秒
        await StartAsync(cancellationToken);
    }

    /// <summary>
    /// 发送数据到指定客户端
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    /// <param name="data">数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    public async Task<bool> SendAsync(string connectionId, byte[] data, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(connectionId);
        ArgumentNullException.ThrowIfNull(data);

        var connection = _connectionManager.GetConnection(connectionId);
        if (connection == null || !connection.IsConnected)
        {
            return false;
        }

        try
        {
            var message = TcpMessage.CreateDataMessage(data);
            return await SendMessageAsync(connection, message, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "发送数据到连接 {ConnectionId} 失败", connectionId);
            await HandleErrorAsync(connection, ex);
            return false;
        }
    }

    /// <summary>
    /// 发送文本到指定客户端
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    /// <param name="text">文本</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    public async Task<bool> SendTextAsync(string connectionId, string text, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(connectionId);
        ArgumentNullException.ThrowIfNull(text);

        var connection = _connectionManager.GetConnection(connectionId);
        if (connection == null || !connection.IsConnected)
        {
            return false;
        }

        try
        {
            var message = TcpMessage.CreateTextMessage(text);
            return await SendMessageAsync(connection, message, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "发送文本到连接 {ConnectionId} 失败", connectionId);
            await HandleErrorAsync(connection, ex);
            return false;
        }
    }

    /// <summary>
    /// 发送消息到指定客户端
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    /// <param name="message">消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    public async Task<bool> SendMessageAsync(string connectionId, TcpMessage message, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(connectionId);
        ArgumentNullException.ThrowIfNull(message);

        var connection = _connectionManager.GetConnection(connectionId);
        if (connection == null || !connection.IsConnected)
        {
            return false;
        }

        return await SendMessageAsync(connection, message, cancellationToken);
    }

    /// <summary>
    /// 广播数据到所有客户端
    /// </summary>
    /// <param name="data">数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    public async Task<int> BroadcastAsync(byte[] data, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(data);

        var message = TcpMessage.CreateDataMessage(data);
        return await BroadcastMessageAsync(message, cancellationToken);
    }

    /// <summary>
    /// 广播文本到所有客户端
    /// </summary>
    /// <param name="text">文本</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    public async Task<int> BroadcastTextAsync(string text, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(text);

        var message = TcpMessage.CreateTextMessage(text);
        return await BroadcastMessageAsync(message, cancellationToken);
    }

    /// <summary>
    /// 广播消息到所有客户端
    /// </summary>
    /// <param name="message">消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    public async Task<int> BroadcastMessageAsync(TcpMessage message, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(message);

        var activeConnections = _connectionManager.GetActiveConnections();
        int successCount = 0;

        var tasks = activeConnections.Select(async connection =>
        {
            if (await SendMessageAsync(connection, message, cancellationToken))
            {
                Interlocked.Increment(ref successCount);
            }
        });

        await Task.WhenAll(tasks);

        _logger?.LogDebug("广播消息到 {TotalConnections} 个连接，成功 {SuccessCount} 个", 
            activeConnections.Count, successCount);

        return successCount;
    }

    /// <summary>
    /// 断开指定客户端连接
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    /// <param name="reason">断开原因</param>
    /// <returns>是否成功断开</returns>
    public async Task<bool> DisconnectClientAsync(string connectionId, string? reason = null)
    {
        ArgumentNullException.ThrowIfNull(connectionId);

        var connection = _connectionManager.GetConnection(connectionId);
        if (connection == null)
        {
            return false;
        }

        try
        {
            // 发送断开连接消息
            if (connection.IsConnected)
            {
                var disconnectMessage = TcpMessage.CreateDisconnectMessage(reason);
                await SendMessageAsync(connection, disconnectMessage, CancellationToken.None);
            }

            // 断开连接
            connection.Disconnect();
            
            // 触发断开连接事件
            await HandleClientDisconnectedAsync(connection, reason, false);

            return true;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "断开连接 {ConnectionId} 时发生异常", connectionId);
            return false;
        }
    }

    /// <summary>
    /// 获取客户端连接信息
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    /// <returns>客户端连接信息</returns>
    public ClientConnection? GetConnection(string connectionId)
    {
        return _connectionManager.GetConnection(connectionId);
    }

    /// <summary>
    /// 获取所有客户端连接信息
    /// </summary>
    /// <returns>所有客户端连接信息</returns>
    public IReadOnlyList<ClientConnection> GetAllConnections()
    {
        return _connectionManager.GetAllConnections();
    }

    /// <summary>
    /// 获取活跃的客户端连接信息
    /// </summary>
    /// <returns>活跃的客户端连接信息</returns>
    public IReadOnlyList<ClientConnection> GetActiveConnections()
    {
        return _connectionManager.GetActiveConnections();
    }

    /// <summary>
    /// 获取服务器统计信息
    /// </summary>
    /// <returns>服务器统计信息</returns>
    public ServerStatistics GetStatistics()
    {
        var connectionStats = _connectionManager.GetStatistics();
        var now = DateTime.UtcNow;
        var uptime = Uptime;

        // 计算平均速率
        double avgSendRate = 0;
        double avgReceiveRate = 0;
        if (uptime.HasValue && uptime.Value.TotalSeconds > 0)
        {
            avgSendRate = _totalBytesSent / uptime.Value.TotalSeconds;
            avgReceiveRate = _totalBytesReceived / uptime.Value.TotalSeconds;
        }

        // 计算错误率
        var totalOperations = _totalMessagesSent + _totalMessagesReceived;
        var errorRate = totalOperations > 0 ? (double)_totalErrors / totalOperations : 0;

        return new ServerStatistics
        {
            TotalConnections = _totalConnections,
            CurrentConnections = ConnectionCount,
            MaxConcurrentConnections = _maxConcurrentConnections,
            TotalBytesSent = _totalBytesSent,
            TotalBytesReceived = _totalBytesReceived,
            TotalMessagesSent = _totalMessagesSent,
            TotalMessagesReceived = _totalMessagesReceived,
            TotalErrors = _totalErrors,
            StartedAt = _startedAt,
            Uptime = uptime,
            AverageConnectionDuration = connectionStats.AverageConnectionDuration,
            AverageSendRate = avgSendRate,
            AverageReceiveRate = avgReceiveRate,
            ErrorRate = errorRate,
            LastUpdatedAt = now
        };
    }

    /// <summary>
    /// 检查客户端连接是否存在
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    /// <returns>是否存在</returns>
    public bool HasConnection(string connectionId)
    {
        return _connectionManager.HasConnection(connectionId);
    }

    /// <summary>
    /// 检查客户端是否已连接
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    /// <returns>是否已连接</returns>
    public bool IsClientConnected(string connectionId)
    {
        return _connectionManager.IsConnectionActive(connectionId);
    }

    /// <summary>
    /// 清理断开的连接
    /// </summary>
    /// <returns>清理的连接数</returns>
    public async Task<int> CleanupDisconnectedConnectionsAsync()
    {
        return await _connectionManager.CleanupDisconnectedConnectionsAsync();
    }

    /// <summary>
    /// 发送心跳到指定客户端
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    public async Task<bool> SendHeartbeatAsync(string connectionId, CancellationToken cancellationToken = default)
    {
        if (_heartbeatHandler == null)
        {
            return false;
        }

        var connection = _connectionManager.GetConnection(connectionId);
        if (connection == null)
        {
            return false;
        }

        return await _heartbeatHandler.SendHeartbeatAsync(connection, cancellationToken);
    }

    /// <summary>
    /// 发送心跳到所有客户端
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    public async Task<int> SendHeartbeatToAllAsync(CancellationToken cancellationToken = default)
    {
        if (_heartbeatHandler == null)
        {
            return 0;
        }

        return await _heartbeatHandler.SendHeartbeatToAllAsync(cancellationToken);
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 异步释放资源
    /// </summary>
    /// <returns>释放任务</returns>
    public async ValueTask DisposeAsync()
    {
        await DisposeAsyncCore();
        Dispose(false);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    /// <param name="disposing">是否正在释放</param>
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            try
            {
                StopAsync().GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "停止服务器时发生异常");
            }

            _heartbeatHandler?.Dispose();
            _cancellationTokenSource?.Dispose();
            _tcpListener = null;

            _disposed = true;
        }
    }

    /// <summary>
    /// 异步释放资源核心方法
    /// </summary>
    /// <returns>释放任务</returns>
    protected virtual async ValueTask DisposeAsyncCore()
    {
        if (!_disposed)
        {
            try
            {
                await StopAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "异步停止服务器时发生异常");
            }

            _heartbeatHandler?.Dispose();
            _cancellationTokenSource?.Dispose();
            _tcpListener = null;

            _disposed = true;
        }
    }

    /// <summary>
    /// 接受客户端连接
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>接受连接任务</returns>
    private async Task AcceptConnectionsAsync(CancellationToken cancellationToken)
    {
        _logger?.LogInformation("开始接受客户端连接");

        try
        {
            while (!cancellationToken.IsCancellationRequested && _tcpListener != null)
            {
                try
                {
                    var tcpClient = await _tcpListener.AcceptTcpClientAsync().ConfigureAwait(false);

                    // 在后台处理客户端连接
                    _ = HandleClientConnectionAsync(tcpClient, cancellationToken);
                }
                catch (ObjectDisposedException)
                {
                    // TCP监听器已被释放，正常退出
                    break;
                }
                catch (InvalidOperationException)
                {
                    // TCP监听器已停止，正常退出
                    break;
                }
                catch (Exception ex)
                {
                    if (!cancellationToken.IsCancellationRequested)
                    {
                        _logger?.LogError(ex, "接受客户端连接时发生异常");
                        await HandleErrorAsync(null, ex).ConfigureAwait(false);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "接受连接循环发生异常");
            await HandleErrorAsync(null, ex).ConfigureAwait(false);
        }

        _logger?.LogInformation("停止接受客户端连接");
    }

    /// <summary>
    /// 处理客户端连接
    /// </summary>
    /// <param name="tcpClient">TCP客户端</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    private async Task HandleClientConnectionAsync(TcpClient tcpClient, CancellationToken cancellationToken)
    {
        ClientConnection? connection = null;

        try
        {
            // 获取客户端IP地址
            var remoteEndPoint = (IPEndPoint)tcpClient.Client.RemoteEndPoint!;
            var clientIp = remoteEndPoint.Address;

            _logger?.LogDebug("新客户端连接: {ClientIp}:{Port}", clientIp, remoteEndPoint.Port);

            // 安全检查
            if (!_securityManager.IsConnectionAllowed(clientIp))
            {
                _logger?.LogWarning("拒绝来自 {ClientIp} 的连接", clientIp);
                tcpClient.Close();
                return;
            }

            // 检查连接数限制
            if (_connectionManager.IsMaxConnectionsReached)
            {
                _logger?.LogWarning("已达到最大连接数限制，拒绝来自 {ClientIp} 的连接", clientIp);
                tcpClient.Close();
                return;
            }

            // 创建客户端连接对象
            connection = new ClientConnection(tcpClient);

            // 配置TCP客户端
            tcpClient.ReceiveBufferSize = _configuration.ReceiveBufferSize;
            tcpClient.SendBufferSize = _configuration.SendBufferSize;
            tcpClient.ReceiveTimeout = _configuration.ConnectionTimeoutSeconds * 1000;
            tcpClient.SendTimeout = _configuration.ConnectionTimeoutSeconds * 1000;

            // 如果启用SSL，创建SSL流
            if (_configuration.EnableSsl && _configuration.SslCertificate != null)
            {
                try
                {
                    var sslStream = await _securityManager.CreateSslStreamAsync(
                        connection.NetworkStream,
                        _configuration.SslCertificate,
                        cancellationToken).ConfigureAwait(false);

                    connection.EnableSsl(sslStream);
                    _logger?.LogDebug("SSL握手成功，连接 {ConnectionId}", connection.Id);
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "SSL握手失败，连接 {ConnectionId}", connection.Id);
                    connection.Dispose();
                    return;
                }
            }

            // 添加到连接管理器
            if (!_connectionManager.AddConnection(connection))
            {
                _logger?.LogWarning("无法添加连接 {ConnectionId} 到连接管理器", connection.Id);
                connection.Dispose();
                return;
            }

            // 更新统计信息
            Interlocked.Increment(ref _totalConnections);
            var currentCount = ConnectionCount;
            if (currentCount > _maxConcurrentConnections)
            {
                _maxConcurrentConnections = currentCount;
            }

            // 记录连接尝试
            _securityManager.RecordConnectionAttempt(clientIp, true);

            // 触发客户端连接事件
            await HandleClientConnectedAsync(connection).ConfigureAwait(false);

            // 处理消息接收
            await ProcessClientMessagesAsync(connection, cancellationToken).ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "处理客户端连接时发生异常");

            if (connection != null)
            {
                await HandleErrorAsync(connection, ex).ConfigureAwait(false);
                await HandleClientDisconnectedAsync(connection, ex.Message, true).ConfigureAwait(false);
            }
        }
        finally
        {
            // 清理连接
            if (connection != null)
            {
                _connectionManager.RemoveConnection(connection.Id);
                connection.Dispose();
            }
        }
    }

    /// <summary>
    /// 处理客户端消息接收
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    private async Task ProcessClientMessagesAsync(ClientConnection connection, CancellationToken cancellationToken)
    {
        var buffer = new byte[_configuration.ReceiveBufferSize];
        var messageBuffer = new List<byte>();

        try
        {
            while (connection.IsConnected && !cancellationToken.IsCancellationRequested)
            {
                var bytesRead = await connection.NetworkStream.ReadAsync(buffer, 0, buffer.Length, cancellationToken).ConfigureAwait(false);

                if (bytesRead == 0)
                {
                    // 客户端断开连接
                    break;
                }

                // 添加到消息缓冲区
                messageBuffer.AddRange(buffer.Take(bytesRead));
                connection.Statistics.RecordReceived(bytesRead);
                Interlocked.Add(ref _totalBytesReceived, bytesRead);

                // 处理完整的消息
                await ProcessCompleteMessagesAsync(connection, messageBuffer, cancellationToken).ConfigureAwait(false);
            }
        }
        catch (Exception ex) when (ex is OperationCanceledException || ex is ObjectDisposedException)
        {
            // 正常的取消或连接关闭
            _logger?.LogDebug("客户端 {ConnectionId} 连接正常关闭", connection.Id);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "处理客户端 {ConnectionId} 消息时发生异常", connection.Id);
            await HandleErrorAsync(connection, ex).ConfigureAwait(false);
        }
    }

    /// <summary>
    /// 处理完整的消息
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="messageBuffer">消息缓冲区</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    private async Task ProcessCompleteMessagesAsync(ClientConnection connection, List<byte> messageBuffer, CancellationToken cancellationToken)
    {
        while (messageBuffer.Count >= 5) // 至少需要消息头（1字节类型 + 4字节长度）
        {
            try
            {
                // 读取消息类型和长度
                var messageType = messageBuffer[0];
                var dataLength = BitConverter.ToInt32(messageBuffer.ToArray(), 1);

                // 检查消息长度是否有效
                if (dataLength < 0 || dataLength > _configuration.MaxMessageLength)
                {
                    _logger?.LogWarning("无效的消息长度: {DataLength}，连接 {ConnectionId}", dataLength, connection.Id);
                    connection.Disconnect();
                    break;
                }

                var totalMessageLength = 5 + dataLength;

                // 检查是否有完整的消息
                if (messageBuffer.Count < totalMessageLength)
                {
                    break; // 等待更多数据
                }

                // 提取完整消息
                var messageBytes = messageBuffer.Take(totalMessageLength).ToArray();
                messageBuffer.RemoveRange(0, totalMessageLength);

                // 反序列化消息
                var message = TcpMessage.Deserialize(messageBytes);
                message.ReceivedAt = DateTime.UtcNow;

                Interlocked.Increment(ref _totalMessagesReceived);

                // 触发数据接收事件
                DataReceived?.Invoke(this, new DataReceivedEventArgs(connection, messageBytes));

                // 处理消息
                await _messageHandler.HandleMessageAsync(connection, message, cancellationToken).ConfigureAwait(false);

                // 如果是心跳响应，通知心跳处理器
                if (message.MessageType == TcpServerConstants.MessageTypes.HeartbeatResponse)
                {
                    _heartbeatHandler?.HandleHeartbeatResponse(connection, message);
                    Heartbeat?.Invoke(this, new HeartbeatEventArgs(connection, false));
                }
                else if (message.MessageType == TcpServerConstants.MessageTypes.HeartbeatRequest)
                {
                    Heartbeat?.Invoke(this, new HeartbeatEventArgs(connection, true));
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "处理消息时发生异常，连接 {ConnectionId}", connection.Id);
                connection.Statistics.RecordReceiveError();
                Interlocked.Increment(ref _totalErrors);
                await HandleErrorAsync(connection, ex).ConfigureAwait(false);
                break;
            }
        }
    }

    /// <summary>
    /// 发送消息到客户端
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="message">消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    private async Task<bool> SendMessageAsync(ClientConnection connection, TcpMessage message, CancellationToken cancellationToken)
    {
        if (!connection.IsConnected)
        {
            return false;
        }

        try
        {
            var data = message.Serialize();

            // 如果启用消息加密，加密数据
            if (_securityManager.Settings.EnableMessageEncryption)
            {
                data = _securityManager.EncryptMessage(data);
            }

            await connection.NetworkStream.WriteAsync(data, 0, data.Length, cancellationToken).ConfigureAwait(false);
            await connection.NetworkStream.FlushAsync(cancellationToken).ConfigureAwait(false);

            connection.Statistics.RecordSent(data.Length);
            message.SentAt = DateTime.UtcNow;

            Interlocked.Add(ref _totalBytesSent, data.Length);
            Interlocked.Increment(ref _totalMessagesSent);

            // 触发数据发送事件
            DataSent?.Invoke(this, new DataSentEventArgs(connection, data, true));

            _logger?.LogDebug("发送消息到连接 {ConnectionId}，长度: {Length}", connection.Id, data.Length);
            return true;
        }
        catch (Exception ex)
        {
            connection.Statistics.RecordSendError();
            Interlocked.Increment(ref _totalErrors);

            // 触发数据发送事件（失败）
            DataSent?.Invoke(this, new DataSentEventArgs(connection, message.Serialize(), false));

            _logger?.LogError(ex, "发送消息到连接 {ConnectionId} 失败", connection.Id);
            return false;
        }
    }

    /// <summary>
    /// 处理客户端连接事件
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <returns>处理任务</returns>
    private async Task HandleClientConnectedAsync(ClientConnection connection)
    {
        try
        {
            _logger?.LogInformation("客户端 {ConnectionId} 已连接，来自 {ClientAddress}",
                connection.Id, connection.ClientIpAddress);

            // 触发连接事件
            ClientConnected?.Invoke(this, new ClientConnectedEventArgs(connection));

            // 处理连接建立
            await _messageHandler.HandleConnectionAsync(connection).ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "处理客户端连接事件时发生异常");
            await HandleErrorAsync(connection, ex).ConfigureAwait(false);
        }
    }

    /// <summary>
    /// 处理客户端断开连接事件
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="reason">断开原因</param>
    /// <param name="isError">是否为错误断开</param>
    /// <returns>处理任务</returns>
    private async Task HandleClientDisconnectedAsync(ClientConnection connection, string? reason, bool isError)
    {
        try
        {
            _logger?.LogInformation("客户端 {ConnectionId} 已断开连接，原因: {Reason}",
                connection.Id, reason ?? "未指定");

            // 触发断开连接事件
            ClientDisconnected?.Invoke(this, new ClientDisconnectedEventArgs(connection, reason, isError));

            // 处理连接断开
            await _messageHandler.HandleDisconnectionAsync(connection, reason).ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "处理客户端断开连接事件时发生异常");
        }
    }

    /// <summary>
    /// 处理错误事件
    /// </summary>
    /// <param name="connection">客户端连接（可选）</param>
    /// <param name="exception">异常信息</param>
    /// <returns>处理任务</returns>
    private async Task HandleErrorAsync(ClientConnection? connection, Exception exception)
    {
        try
        {
            Interlocked.Increment(ref _totalErrors);

            _logger?.LogError(exception, "发生错误: {ErrorMessage}", exception.Message);

            // 触发错误事件
            Error?.Invoke(this, new TcpServerErrorEventArgs(exception, connection));

            // 处理错误
            await _messageHandler.HandleErrorAsync(connection, exception).ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "处理错误事件时发生异常");
        }
    }

    /// <summary>
    /// 更改服务器状态
    /// </summary>
    /// <param name="newStatus">新状态</param>
    private void ChangeStatus(string newStatus)
    {
        var oldStatus = _status;
        _status = newStatus;

        if (oldStatus != newStatus)
        {
            _logger?.LogInformation("服务器状态变更: {OldStatus} -> {NewStatus}", oldStatus, newStatus);
            StatusChanged?.Invoke(this, new ServerStatusChangedEventArgs(oldStatus, newStatus));
        }
    }

    /// <summary>
    /// 心跳发送事件处理
    /// </summary>
    /// <param name="sender">发送者</param>
    /// <param name="e">事件参数</param>
    private void OnHeartbeatSent(object? sender, HeartbeatSentEventArgs e)
    {
        Heartbeat?.Invoke(this, new HeartbeatEventArgs(e.Connection, true));
    }

    /// <summary>
    /// 心跳超时事件处理
    /// </summary>
    /// <param name="sender">发送者</param>
    /// <param name="e">事件参数</param>
    private void OnHeartbeatTimeout(object? sender, HeartbeatTimeoutEventArgs e)
    {
        _logger?.LogWarning("连接 {ConnectionId} 心跳超时", e.Connection.Id);

        // 断开超时的连接
        _ = DisconnectClientAsync(e.Connection.Id, "心跳超时");
    }
}
